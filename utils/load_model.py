from transformers import AutoTokenizer, AutoModelForCausalLM
import torch
import os

def load_llama_model_and_tokenizer(model_path, device):
    """
    加载LLaMA模型和分词器

    Args:
        model_path: 模型路径
        device: 设备，可以是 "cuda", "cuda:0", "cuda:1" 等
    """
    tokenizer = AutoTokenizer.from_pretrained(model_path)

    # 如果指定了具体的GPU设备，使用device_map参数
    if device.startswith("cuda:"):
        device_map = {f"": int(device.split(":")[1])}
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            device_map=device_map,
            torch_dtype=torch.float16
        )
    else:
        # 使用原来的方式
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            device_map=device,
            torch_dtype=torch.float16
        )

    print(f"模型权重位数: {model.dtype}")
    print(f"模型加载到设备: {device}")

    return model, tokenizer
